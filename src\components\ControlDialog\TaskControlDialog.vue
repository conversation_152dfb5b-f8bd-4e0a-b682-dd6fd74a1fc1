<template>
  <!-- 实体控制对话框 -->
  <el-dialog
    v-model="store.state.app.taskControlBoxShow"
    :close-on-click-modal="false"
    title="任务控制"
    width="40%"
    draggable
    top="10vh"
    append-to-body
  >
    <el-row class="controlBox">
      <div class="left">
        <div class="title">实体列表</div>
        <div class="left-content">
          <el-row class="tabs">
            <div
              v-for="item in sideList"
              :key="item.value"
              class="tab"
              :style="{ color: item.color }"
              :class="[{ active: currentSide === item.value }]"
              @click="currentSide = item.value"
            >
              {{ item.label }}
            </div>
          </el-row>
          <template v-if="sideEntities.length">
            <div class="entityList">
              <div
                v-for="item in sideEntities"
                :key="item"
                class="entity"
                :title="item"
                :style="{
                  color: sideColor,
                  background: item === currentEntity ? '#0175b8' : '',
                }"
                @click="currentEntity = item"
              >
                {{ item }}
              </div>
            </div>
          </template>
          <template v-else>
            <div class="emptyBox">
              <Empty />
            </div>
          </template>
        </div>
      </div>
      <div class="right">
        <div class="title">任务控制</div>
        <div class="collapseBox">
          <el-collapse accordion>
            <el-collapse-item>
              <template #title>
                <el-row style="width: 100%">
                  <el-col :span="8">到达高度</el-col>
                  <el-col :span="16">当前高度：{{ currentAlt }}</el-col>
                </el-row>
              </template>
              <el-form :model="altForm" label-width="80px">
                <el-form-item label="高度">
                  <el-input
                    v-model.number="altForm.alt"
                    type="number"
                    :controls="false"
                    placeholder="请输入高度"
                    ><template #suffix>
                      <span>米</span>
                    </template></el-input
                  >
                </el-form-item>
                <el-form-item label="加速度">
                  <el-input
                    v-model.number="altForm.acceleration"
                    type="number"
                    :controls="false"
                    placeholder="请输入加速度"
                    ><template #suffix>
                      <span>米/平方秒</span>
                    </template></el-input
                  >
                </el-form-item>
                <!-- <el-form-item label="是否匀加速">
                  <el-switch v-model="altForm.isAcceleration"></el-switch>
                </el-form-item> -->
                <el-form-item label="保持航路">
                  <img
                    :src="`/image/${
                      altForm.isKeepRoute ? '' : 'un'
                    }selected_icon.png`"
                    width="15"
                    alt=""
                    style="cursor: pointer"
                    @click="altForm.isKeepRoute = !altForm.isKeepRoute"
                  />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="sendAltForm"
                    >发送</el-button
                  >
                </el-form-item>
              </el-form>
            </el-collapse-item>
            <el-collapse-item
              ><template #title>
                <el-row style="width: 100%">
                  <el-col :span="8">到达位置</el-col>
                  <el-col :span="16">当前位置：{{ currentLocation }}</el-col>
                </el-row>
              </template>
              <el-form
                :model="locationForm"
                label-width="80px"
                ref="positionRef"
                :rules="positionRules"
              >
                <el-form-item label="经度" prop="lon">
                  <el-input
                    v-model="locationForm.lon"
                    type="number"
                    :controls="false"
                    placeholder="请输入经度"
                  ></el-input>
                </el-form-item>
                <el-form-item label="纬度" prop="lat">
                  <el-input
                    v-model="locationForm.lat"
                    type="number"
                    :controls="false"
                    placeholder="请输入纬度"
                  ></el-input>
                </el-form-item>
                <el-form-item label="高度">
                  <el-input
                    v-model.number="locationForm.alt"
                    type="number"
                    :controls="false"
                    placeholder="请输入高度"
                    ><template #suffix>
                      <span>米</span>
                    </template></el-input
                  >
                </el-form-item>
                <!-- <el-form-item label="高度">
                  <el-switch v-model="locationForm.isAlt"></el-switch>
                </el-form-item> -->
                <el-form-item>
                  <el-button type="primary" @click="sendLocationForm"
                    >发送</el-button
                  >
                </el-form-item>
              </el-form>
            </el-collapse-item>
            <el-collapse-item
              ><template #title>
                <el-row style="width: 100%">
                  <el-col :span="8">到达速度</el-col>
                  <el-col :span="16">当前速度：{{ currentSpeed }}</el-col>
                </el-row>
              </template>
              <el-form :model="speedForm" label-width="80px">
                <el-form-item label="速度">
                  <el-input
                    v-model.number="speedForm.speed"
                    type="number"
                    :controls="false"
                    placeholder="请输入速度"
                    ><template #suffix>
                      <span>米/秒</span>
                    </template></el-input
                  >
                </el-form-item>
                <el-form-item label="加速度">
                  <el-input
                    v-model.number="speedForm.acceleration"
                    type="number"
                    :controls="false"
                    placeholder="请输入加速度"
                    ><template #suffix>
                      <span>米/平方秒</span>
                    </template></el-input
                  >
                </el-form-item>
                <!-- <el-form-item label="加速度">
                  <el-switch v-model="speedForm.isAcceleration"></el-switch>
                </el-form-item> -->
                <el-form-item label="保持航路">
                  <img
                    :src="`/image/${
                      speedForm.isKeepRoute ? '' : 'un'
                    }selected_icon.png`"
                    width="15"
                    alt=""
                    style="cursor: pointer"
                    @click="speedForm.isKeepRoute = !speedForm.isKeepRoute"
                  />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="sendSpeedForm"
                    >发送</el-button
                  >
                </el-form-item>
              </el-form>
            </el-collapse-item>
            <el-collapse-item>
              <template #title>
                <el-row style="width: 100%">
                  <el-col :span="8">调整航向</el-col>
                  <el-col :span="16">当前航向：{{ currentHeading }}</el-col>
                </el-row>
              </template>
              <el-form :model="headingForm" label-width="80px">
                <el-form-item label="航向">
                  <el-input
                    v-model.number="headingForm.heading"
                    type="number"
                    :controls="false"
                    placeholder="请输入航向"
                    ><template #suffix>
                      <span>度</span>
                    </template></el-input
                  >
                </el-form-item>
                <el-form-item label="加速度">
                  <el-input
                    v-model.number="headingForm.acceleration"
                    type="number"
                    :controls="false"
                    placeholder="请输入加速度"
                    ><template #suffix>
                      <span>米/平方秒</span>
                    </template></el-input
                  >
                </el-form-item>
                <!-- <el-form-item label="加速度">
                  <el-switch v-model="headingForm.isAcceleration"></el-switch>
                </el-form-item> -->
                <el-form-item>
                  <el-button type="primary" @click="sendHeadingForm"
                    >发送</el-button
                  >
                </el-form-item>
              </el-form>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
    </el-row>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { cloneDeep } from 'lodash-es'
import Empty from '@/components/Empty.vue'
import { useStore } from 'vuex'
import * as Api from '@/api/dt'
const store = useStore()

// 组件属性定义
const props = defineProps({
  // 实体列表数据
  entitiesList: {
    type: Object,
    default: [],
  },
  // 实体类型列表
  entitiesTypeList: {
    type: Object,
    default: [],
  },
})

const sideList = [
  {
    label: '红方',
    value: 'red',
    color: '#fe7f7f',
  },
  {
    label: '蓝方',
    value: 'blue',
    color: '#00a4c5',
  },
]
const currentSide = ref('red')
const sideEntities = computed(() => {
  return props.entitiesList
    .filter(i => i.si === currentSide.value)
    .map(i => i.na)
})
const sideColor = computed(() => {
  return sideList.find(i => i.value === currentSide.value)?.color || '#fff'
})

// 当前激活的标签页名称（红方/蓝方）
const activeName = ref('red')
// 控制创建表单的显示状态
const createFlag = ref(false)
// 选中实体
const currentEntity = ref('')
// 创建实体的表单数据
const createForm = reactive({
  name: '', // 名称
  type: '', // 类型
  side: activeName.value, // 阵营
  lon: 0, // 经度
  lat: 0, // 纬度
  alt: 0, // 高度
  ori: 0, // 朝向
  speed: 0, // 速度
})
// 到达高度form
const defalutAltForm = {
  alt: 0,
  acceleration: 0,
  isAcceleration: true,
  isKeepRoute: true,
}
let altForm = reactive(cloneDeep(defalutAltForm))
// 到达位置form
const defaultLocation = {
  lon: 0,
  lat: 0,
  alt: 0,
  isAlt: true,
}
let locationForm = reactive(cloneDeep(defaultLocation))
// 到达速度form
const defaultSpeed = {
  speed: 0,
  acceleration: 0,
  isAcceleration: true,
  isKeepRoute: true,
}
let speedForm = reactive(cloneDeep(defaultSpeed))
// 调整航向form
const defaultHeading = {
  heading: 0,
  acceleration: 0,
  isAcceleration: true,
}
let headingForm = reactive(cloneDeep(defaultHeading))

// 标签页数据
const editableTabs: any = reactive([
  {
    title: '红方',
    name: 'red',
    content: [],
  },
  {
    title: '蓝方',
    name: 'blue',
    content: [],
  },
])
const MAX_UPDATE_RETRY = 3
let frameId: number

// 当前高度
const currentAlt = ref('0.0米')
const currentLocation = ref('0,0,0米')
const currentHeading = ref('0.00度') //航向
const currentSpeed = ref('0米/秒') //速度
const positionRef = ref()
const update = (retry = 0) => {
  if (!window.viewer) return
  const entity = window.viewer.entities.getById(currentEntity.value)
  const clickEntity = entity && entity.properties
  currentAlt.value = clickEntity.al.getValue()
  const longitude = clickEntity.lo.getValue()
  const latitude = clickEntity.la.getValue()
  const altitude = clickEntity.al.getValue()
  const speed = clickEntity.sp.getValue()
  if (speed) currentSpeed.value = `${speed.toFixed(2)}米/秒`
  const heading = clickEntity.ya.getValue()
  if (heading) currentHeading.value = `${heading.toFixed(2)}度`
  currentLocation.value = `${(longitude || 0).toFixed(2)},${(
    latitude || 0
  ).toFixed(2)},${(altitude || 0).toFixed(2)}米`
  if (!clickEntity) {
    if (retry < MAX_UPDATE_RETRY) {
      setTimeout(() => update(retry + 1), 200)
    }
    return
  }

  frameId = requestAnimationFrame(() => update())
}

/** 发送高度 */
const sendAltForm = async () => {
  if (!currentEntity.value) {
    ElMessage.warning('请选择实体')
    return
  }
  if (!altForm.alt) {
    ElMessage.warning('请输入高度')
    return
  }
  try {
    await Api.sendEventApi({
      type: 'ExecuteScript',
      content: {
        platform_name: '',
        processor_name: '',
        script_args: [
          currentEntity.value,
          altForm.alt,
          altForm.acceleration,
          altForm.isKeepRoute,
        ],
        script_name: 'GoToAltitude',
        type: 3,
      },
    })
    ElMessage.success('发送成功')
  } catch (error) {
    ElMessage.error('发送失败')
  }
}
const positionRules = reactive({
  lon: [
    { required: true, message: '请输入经度', trigger: 'blur' },
    { min: -180, max: 180, message: '范围为-180到180', trigger: 'blur' },
  ],
  lat: [
    { required: true, message: '请输入纬度', trigger: 'blur' },
    { min: -90, max: 90, message: '范围为-90到90', trigger: 'blur' },
  ],
})
/** 发送位置 */
const sendLocationForm = () => {
  positionRef.value.validate(async (valid: any) => {
    console.log('🚀 ~ valid:', valid)
    if (valid) {
      if (!currentEntity.value) {
        ElMessage.warning('请选择实体')
        return
      }
      if (!locationForm.lon && locationForm.lon !== 0) {
        ElMessage.warning('请输入经度')
        return
      }
      if (!locationForm.lat && locationForm.lat !== 0) {
        ElMessage.warning('请输入纬度')
        return
      }
      try {
        await Api.sendEventApi({
          type: 'ExecuteScript',
          content: {
            platform_name: '',
            processor_name: '',
            script_args: [
              currentEntity.value,
              locationForm.lon,
              locationForm.lat,
              locationForm.alt,
            ],
            script_name: 'GoToLocation',
            type: 3,
          },
        })
        ElMessage.success('发送成功')
      } catch (error) {
        ElMessage.error('发送失败')
      }
      // 这里执行提交操作
    } else {
      ElMessage.error('数据校验失败,请重新输入!')
      return false
    }
  })
}

/** 发送速度 */
const sendSpeedForm = async () => {
  if (!currentEntity.value) {
    ElMessage.warning('请选择实体')
    return
  }
  if (!speedForm.speed) {
    ElMessage.warning('请输入速度')
    return
  }
  try {
    await Api.sendEventApi({
      type: 'ExecuteScript',
      content: {
        platform_name: '',
        processor_name: '',
        script_args: [
          currentEntity.value,
          speedForm.speed,
          speedForm.acceleration,
          speedForm.isKeepRoute,
        ],
        script_name: 'GoToSpeed',
        type: 3,
      },
    })
    ElMessage.success('发送成功')
  } catch (error) {
    ElMessage.error('发送失败')
  }
}

/** 发送航向 */
const sendHeadingForm = async () => {
  if (!currentEntity.value) {
    ElMessage.warning('请选择实体')
    return
  }
  if (!headingForm.heading) {
    ElMessage.warning('请输入航向')
    return
  }
  try {
    await Api.sendEventApi({
      type: 'ExecuteScript',
      content: {
        platform_name: '',
        processor_name: '',
        script_args: [
          currentEntity.value,
          headingForm.heading,
          headingForm.acceleration,
        ],
        script_name: 'GoToHeading',
        type: 3,
      },
    })
    ElMessage.success('发送成功')
  } catch (error) {
    ElMessage.error('发送失败')
  }
}

// 监听实体列表变化，更新红蓝方实体分类
watch(
  () => props.entitiesList,
  val => {
    // 过滤红方实体
    editableTabs[0].content = val
      .filter(item => item.si == 'red')
      .map(item => item.na)
    // 过滤蓝方实体
    editableTabs[1].content = val
      .filter(item => item.si == 'blue')
      .map(item => item.na)
    if (!val.length) return (currentEntity.value = '')
    if (currentEntity.value && val.find(item => item.na == currentEntity.value))
      return
    if (editableTabs[0].content.length) {
      currentEntity.value = editableTabs[0].content[0]
      activeName.value = 'red'
      return
    }
    if (editableTabs[1].content.length) {
      currentEntity.value = editableTabs[1].content[0]
      activeName.value = 'blue'
      return
    }
  },
  {
    immediate: true, // 立即执行一次
    deep: true, // 深度监听
  }
)
watch(
  () => currentEntity.value,
  newId => {
    if (newId) {
      if (store.state.app.taskControlBoxShow) {
        cancelAnimationFrame(frameId)
        update()
      } else {
        cancelAnimationFrame(frameId)
      }
    }
  },
  {
    immediate: true,
  }
)
// 监听标签页切换，更新表单中的阵营值
watch(
  () => activeName.value,
  () => {
    createForm.side = activeName.value
  }
)

// 监听对话框显示状态，重置表单
watch(
  () => store.state.app.entityControlBoxShow,
  val => {
    if (!val) return
    createFlag.value = false
    resetForm()
  }
)
watch(
  () => store.state.app.taskControlBoxShow,
  val => {
    if (!val) return
    update()
  }
)

/**
 * 重置表单数据
 */
const resetForm = () => {
  createForm.name = ''
  createForm.type = ''
  createForm.side = activeName.value
  createForm.lon = 0
  createForm.lat = 0
  createForm.alt = 0
  createForm.ori = 0
  createForm.speed = 0
}

/**
 * 创建新实体
 * 验证表单数据并发送创建请求
 */
const createEntity = async () => {
  // 表单验证
  if (!createForm.name) {
    ElMessage.warning('请输入实体名称')
    return
  }
  if (!createForm.type) {
    ElMessage.warning('请选择实体类型')
    return
  }

  // 确认创建
  ElMessageBox.confirm('确定要创建该实体吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        // 发送创建实体请求
        await Api.sendEventApi({
          type: 'ExecuteScript',
          content: {
            platform_name: '',
            processor_name: '',
            script_args: [
              createForm.name,
              createForm.type,
              createForm.side,
              createForm.lon || 0,
              createForm.lat || 0,
              createForm.alt || 0,
              createForm.ori || 0,
              createForm.speed || 0,
            ],
            script_name: 'AddPlatform',
            type: 3,
          },
        })
        ElMessage.success('创建成功')
      } catch (error) {
        ElMessage.error('创建失败')
      }
    })
    .catch(() => {
      // 用户取消操作，不做任何处理
    })
}
</script>

<style lang="less" scoped>
.controlBox {
  height: 500px;
  /* 左侧卡片样式 */
  .left {
    height: 100%;
    width: 40%;
    border-right: 1px solid var(--app-border-color);
    .title {
      line-height: 34px;
      background-color: #145d89;
      font-size: 16px;
      padding-left: 10px;
      margin-bottom: 15px;
    }
    .left-content {
      padding-right: 10px;
      height: calc(100% - 34px - 15px);
      .tabs {
        .tab {
          width: 87px;
          height: 30px;
          line-height: 30px;
          background: #033a5a;
          border: solid 1px var(--app-border-color);
          border-bottom: none;
          cursor: pointer;
          text-align: center;
          margin-right: 10px;
          border-top-left-radius: 2px;
          border-top-right-radius: 2px;
          font-weight: 600;
        }
        .active {
          background: #125d8d;
        }
      }
      .entityList {
        height: calc(100% - 30px - 10px);
        border: 1px solid var(--app-border-color);
        overflow-y: auto;
        padding: 5px 10px;
        .entity {
          padding-left: 5px;
          cursor: pointer;
          line-height: 28px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .entity:hover {
          background: #015586;
        }
      }
      .emptyBox {
        padding-top: 50px;
        height: calc(100% - 30px - 10px);
        border: 1px solid var(--app-border-color);
        display: flex;
        flex-direction: column;
        align-items: center;
      }
    }
  }

  /* 右侧卡片样式 */
  .right {
    width: 60%;
    .title {
      line-height: 34px;
      background-color: #145d89;
      font-size: 16px;
      padding-left: 10px;
      margin-bottom: 15px;
    }
    .collapseBox {
      height: calc(100% - 34px - 15px);
      :deep(.el-collapse) {
        overflow: auto;
        border: unset;
        height: 100%;

        .el-collapse-item {
          padding: unset;
          margin-bottom: 5px;
          border: unset;

          .el-collapse-item__header {
            position: relative;
            height: 33px;
            padding-left: 18px;
            /* stylelint-disable-next-line font-family-no-missing-generic-family-keyword */
            font-size: 16px;
            color: #fff;
            background-color: #003f65;
            align-items: center;
            border-bottom: unset;
            .el-col {
              font-size: 15px;
            }
          }
          .el-collapse-item__wrap {
            padding: 0;
            background: transparent;
            border-bottom: unset;

            .el-collapse-item__content {
              padding: 10px 0 0 0;
              .el-form {
                .el-form-item {
                  margin-bottom: 10px;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
